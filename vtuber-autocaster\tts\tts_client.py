import os, tempfile, subprocess
import pyttsx3
try:
    # Try to import the full ElevenLabs SDK
    from elevenlabs import VoiceSettings, save
    from elevenlabs.client import ElevenLabs
    ELEVENLABS_SDK_AVAILABLE = True
except ImportError:
    # Fall back to simple API client if <PERSON>K fails
    ELEVENLABS_SDK_AVAILABLE = False

# Always import these
from .elevenlabs_simple import SimpleElevenLabsClient
from .elevenlabs_agent import ElevenLabsAgentClient

class TTSClient:
    def __init__(self, engine: str = "pyttsx3", api_key: str = None):
        self.engine_name = engine
        if engine == "elevenlabs":
            if not api_key:
                api_key = os.getenv("ELEVENLABS_API_KEY")
            if not api_key:
                raise RuntimeError("ElevenLabs API key required. Set ELEVENLABS_API_KEY env variable.")
            
            # Initialize both regular and agent clients
            self.agent_client = ElevenLabsAgentClient(api_key=api_key)
            
            # Always use simple client for better compatibility
            print("Using simple ElevenLabs API client for better compatibility...")
            self.client = SimpleElevenLabsClient(api_key=api_key)
            self.engine = None
        elif engine == "pyttsx3":
            self.engine = pyttsx3.init()
            self.client = None
        else:
            # Fallback to pyttsx3
            self.engine = pyttsx3.init()
            self.client = None

    def synthesize(self, text: str, rate: int = 130, voice_id: str | None = None, 
                   model: str = "eleven_turbo_v2_5", voice_settings: dict = None) -> str:
        """Return path to a temp WAV file synthesized with chosen engine."""
        
        if self.engine_name == "elevenlabs":
            # Use ElevenLabs API
            if not voice_id:
                voice_id = "EXAVITQu4vr4xnSDxMaL"  # Default to Sarah voice
            
            # Check if this is an agent ID (starts with 'agent_' or is one of our known agents)
            known_agents = ["SRojRJ0pzQailSLw3fo0", "agent_19692_295071229269264481", "agent_8501k5vp3n7fegtvvv6gxrz506h5", "efD2roF1M5pMoNxfHprk"]
            is_agent = voice_id.startswith("agent_") or voice_id in known_agents
            
            if is_agent:
                # Use agent handler for conversational AI agents
                print(f"   Using ElevenLabs agent: {voice_id}")
                return self.agent_client.generate_from_agent(text, voice_id)
            else:
                # Regular voice IDs - always use simple client for compatibility
                print(f"   Using voice ID: {voice_id}")
                return self.client.generate(text, voice_id, model, voice_settings)

            # Legacy SDK code (not used due to compatibility issues)
            if False and ELEVENLABS_SDK_AVAILABLE:
                # Use full SDK if available
                settings = None
                if voice_settings:
                    settings = VoiceSettings(
                        stability=voice_settings.get("stability", 0.5),
                        similarity_boost=voice_settings.get("similarity_boost", 0.75),
                        style=voice_settings.get("style", 0.0),
                        use_speaker_boost=voice_settings.get("use_speaker_boost", True)
                    )
                
                # Generate audio
                audio = self.client.generate(
                    text=text,
                    voice=voice_id,
                    model=model,
                    voice_settings=settings
                )
                
                # Save to temp file
                out_wav = os.path.join(tempfile.gettempdir(), f"tts_{os.getpid()}_{abs(hash(text))}.wav")
                save(audio, out_wav)
                return out_wav
            
        elif self.engine_name == "pyttsx3":
            self.engine.setProperty("rate", rate)
            if voice_id:
                self.engine.setProperty("voice", voice_id)
            out_wav = os.path.join(tempfile.gettempdir(), f"tts_{os.getpid()}_{abs(hash(text))}.wav")
            self.engine.save_to_file(text, out_wav)
            self.engine.runAndWait()
            return out_wav
        else:
            raise NotImplementedError(f"Engine {self.engine_name} not implemented.")

    def abyss_post_fx(self, in_wav: str, pitch_ratio: float = 0.8, reverb: str = "0.8:0.9:1000:0.25") -> str:
        """Lower pitch + reverb via ffmpeg aecho. Returns processed wav path."""
        try:
            out_wav = in_wav.replace('.wav', '_abyss.wav').replace('.mp3', '_abyss.mp3')
            cmd = [
                'ffmpeg','-y','-i', in_wav,
                '-af', f"asetrate=44100*{pitch_ratio},aresample=44100,aecho={reverb}",
                out_wav
            ]
            result = subprocess.run(cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, check=True)
            return out_wav
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("Warning: ffmpeg not available for post-processing, using original audio")
            return in_wav  # Return original if ffmpeg fails
