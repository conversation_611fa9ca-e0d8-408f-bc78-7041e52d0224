"""
Simple VTuber Autocaster - No LLM Required
Just uses local responses and TTS
"""

import os
import time
import random
import yaml
from tts.tts_client import TTSClient
from utils.persona import enforce_grimm
from utils.timers import sleep_for, next_delay
from avatar_integration import initialize_avatar_display, get_avatar_display

def load_config():
    with open("config/app.yaml", "r", encoding="utf-8") as f:
        return yaml.safe_load(f)

def choose_character(cfg):
    """Choose which character speaks next"""
    mode = cfg["routing"]["character_mode"]
    if mode == "rotate":
        return random.choice(["sweet", "grimm", "abyss"])
    else:
        return mode

def get_simple_response(character):
    """Get simple local responses without LLM"""
    if character == "grimm":
        # Load Grimm variants
        with open("config/characters/grimm_variants.txt", "r", encoding="utf-8") as f:
            grimm_variants = [l.strip() for l in f if l.strip()]
        return enforce_grimm(grimm_variants)
    
    elif character == "sweet":
        responses = [
            "Duuude... that's like... pretty intense, man.",
            "Whoa... *munch* ...that's some heavy stuff right there.",
            "That's... that's actually pretty sweet, dude.",
            "Man, I could really go for some chips right now...",
            "Duuude... what if we're all just... you know... in a simulation?",
            "That's like... cosmic, man. *crunch*",
            "Whoa... that just blew my mind, dude.",
            "Pretty chill vibes, not gonna lie.",
        ]
        return random.choice(responses)
    
    elif character == "abyss":
        responses = [
            "The void witnesses your pathetic struggles.",
            "Your actions echo through the infinite darkness.",
            "Mortals... always so predictable in their chaos.",
            "The cosmic indifference consumes all meaning.",
            "Your triumph dissolves like starlight in the void.",
            "The eternal darkness judges your fleeting existence.",
            "Reality bends to the will of the Shadow God.",
            "Your screams are but whispers in the infinite abyss.",
        ]
        return random.choice(responses)
    
    return "..."

def main():
    print("🎮 Simple VTuber Autocaster Starting...")
    
    # Load configuration
    cfg = load_config()
    
    # Load Grimm variants
    with open("config/characters/grimm_variants.txt", "r", encoding="utf-8") as f:
        grimm_variants = [l.strip() for l in f if l.strip()]

    # Initialize TTS with API key if using ElevenLabs
    api_key = os.getenv(cfg["tts"].get("api_key_env")) if cfg["tts"]["engine"] == "elevenlabs" else None
    tts = TTSClient(engine=cfg["tts"]["engine"], api_key=api_key)

    print("Simple Autocaster running. Set Windows default playback to VB-Cable for lipsync.")
    
    # Initialize avatar display
    print("\n🎮 Starting Avatar Display System...")
    avatar_display_ready = initialize_avatar_display()
    avatar_display = get_avatar_display()
    
    if avatar_display_ready:
        print("\n✨ AVATAR SYSTEM READY!")
        print("📺 To see avatars on screen:")
        print("   1. Open avatar_display.html in your browser")
        print("   2. OR add as Browser Source in OBS")
        print("   3. Set to 1920x1080, check 'Shutdown when not visible'")
        print("")
    else:
        print("❌ Avatar display failed to start")
        print("")

    print("🎭 Characters will speak every 10-60 seconds with simple responses")
    print("Press Ctrl+C to stop\n")

    while True:
        try:
            char = choose_character(cfg)
            
            # Get timing based on character
            if char == "grimm":
                delay = next_delay(cfg["autonomy"]["grimm_min"], cfg["autonomy"]["grimm_max"])
            elif char == "sweet":
                delay = next_delay(cfg["autonomy"]["sweet_min"], cfg["autonomy"]["sweet_max"])
            else:
                delay = next_delay(cfg["autonomy"]["abyss_min"], cfg["autonomy"]["abyss_max"])
            
            sleep_for(delay)

            # Get simple response
            line = get_simple_response(char)
            
            # Keep lines tight
            line = line.split("\n")[0].strip()
            if len(line) > 180:
                line = line[:180]

            print(f"\n🎭 {char.upper()}: {line}")

            # TTS
            voice_id = cfg["tts"]["voice"][char]
            voice_settings = cfg["tts"]["voice_settings"].get(char, {})
            model = cfg["tts"].get("model", "eleven_turbo_v2_5")
            
            wav_path = tts.synthesize(
                text=line,
                voice_id=voice_id,
                model=model,
                voice_settings=voice_settings
            )

            # Apply Abyss post-processing if needed
            if char == "abyss" and cfg["tts"]["post_fx"]["abyss"].get("enabled", True):
                wav_path = tts.abyss_post_fx(
                    wav_path,
                    pitch_ratio=cfg["tts"]["post_fx"]["abyss"]["pitch_ratio"],
                    reverb=cfg["tts"]["post_fx"]["abyss"]["reverb"]
                )

            # Send to avatar display
            if avatar_display:
                avatar_display.show_character(char, line)

            # Play audio
            from utils.audio import play_wav
            play_wav(wav_path)

        except KeyboardInterrupt:
            print("\n👋 Stopping autocaster...")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            time.sleep(5)  # Wait before retrying

if __name__ == "__main__":
    main()
