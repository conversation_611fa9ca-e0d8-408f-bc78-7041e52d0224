# VTuber Autocaster Configuration
api_keys:
  openai: "********************************************************************************************************************************************************************"
  elevenlabs: "***************************************************"

# Character routing
routing:
  character_mode: "rotate"  # "rotate", "abyss", "grimm", "sweet"

# Character settings
characters:
  mode: "rotate"  # "rotate", "abyss", "grimm", "sweet"

  abyss:
    name: "Mr. Abyss"
    voice_id: "efD2roF1M5pMoNxfHprk"
    model_path: "../mrabyss/hooded-figure-with-scythe/source/hooded figure with sythe.glb"
    personality: "cosmic_entity"

  grimm:
    name: "<PERSON>"
    voice_id: "agent_8501k5vp3n7fegtvvv6gxrz506h5"
    model_path: "../PGD/Grimm/Animation_Walking_withSkin.fbx"
    personality: "creepy_doll"

  sweet:
    name: "Sweet"
    voice_id: "SRojRJ0pzQailSLw3fo0"
    model_path: "../biped/Animation_Walking_withSkin.fbx"
    personality: "stoned_philosopher"

# Timing settings
timing:
  min_interval: 15  # seconds
  max_interval: 45  # seconds

# Vision settings
vision:
  enabled: true
  screenshot_interval: 5  # seconds

# Voice settings
voice:
  stability: 0.4
  similarity_boost: 0.6
  style: 0.2
  use_speaker_boost: true

# Autonomy settings (timing between comments)
autonomy:
  grimm_min: 10  # seconds
  grimm_max: 30
  sweet_min: 15
  sweet_max: 45
  abyss_min: 20
  abyss_max: 60

# LLM settings
llm:
  provider: "openai"  # "openai" or "anthropic"
  model: "gpt-4"
  api_key_env: "OPENAI_API_KEY"
  temperature:
    abyss: 0.8
    sweet: 0.7
    grimm: 0.5
  max_completion_tokens: 120

# Post-processing for Mr. Abyss
abyss_effects:
  reverb: true
  pitch_shift: -0.2
  echo_delay: 0.3

# TTS settings
tts:
  engine: "elevenlabs"
  api_key_env: "ELEVENLABS_API_KEY"
  model: "eleven_turbo_v2_5"
  voice:
    abyss: "efD2roF1M5pMoNxfHprk"
    grimm: "agent_8501k5vp3n7fegtvvv6gxrz506h5"
    sweet: "SRojRJ0pzQailSLw3fo0"
  voice_settings:
    abyss:
      stability: 0.4
      similarity_boost: 0.6
      style: 0.2
      use_speaker_boost: true
    grimm:
      stability: 0.5
      similarity_boost: 0.7
      style: 0.1
      use_speaker_boost: true
    sweet:
      stability: 0.3
      similarity_boost: 0.5
      style: 0.4
      use_speaker_boost: true
  post_fx:
    abyss:
      enabled: true
      pitch_ratio: 0.8
      reverb: true

# 3D Avatar settings
avatars:
  enabled: true
  window_width: 500
  window_height: 700
  position: "bottom_right"