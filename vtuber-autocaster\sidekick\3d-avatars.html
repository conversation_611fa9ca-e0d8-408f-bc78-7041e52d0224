<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Grimm & Abyss - Real 3D Avatars</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/FBXLoader.js"></script>
  <style>
    body {
      margin: 0;
      padding: 0;
      background: #000;
      overflow: hidden;
    }
    
    #container {
      position: fixed;
      bottom: 0;
      right: 0;
      width: 500px;
      height: 700px;
      background: transparent;
    }
    
    #speech {
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0,0,0,0.9);
      color: white;
      padding: 10px 20px;
      border-radius: 10px;
      max-width: 400px;
      display: none;
      font-family: Arial;
      border: 2px solid #fff;
      z-index: 1000;
    }
    
    #speech.grimm {
      border-color: #ff6666;
      box-shadow: 0 0 30px rgba(255,100,100,0.8);
    }
    
    #speech.abyss {
      border-color: #8800ff;
      box-shadow: 0 0 30px rgba(138,43,226,0.8);
    }
  </style>
</head>
<body>
  <div id="container"></div>
  <div id="speech"></div>
  
  <script>
    // Three.js scene setup
    const scene = new THREE.Scene();
    scene.background = null; // Transparent
    
    const camera = new THREE.PerspectiveCamera(
      75, 
      window.innerWidth / window.innerHeight, 
      0.1, 
      1000
    );
    
    const renderer = new THREE.WebGLRenderer({ 
      alpha: true,
      antialias: true 
    });
    
    const container = document.getElementById('container');
    renderer.setSize(500, 700);
    container.appendChild(renderer.domElement);
    
    // Lighting
    const ambientLight = new THREE.AmbientLight(0x404040, 2);
    scene.add(ambientLight);
    
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
    directionalLight.position.set(5, 10, 5);
    scene.add(directionalLight);
    
    const pointLight = new THREE.PointLight(0xff0000, 1);
    pointLight.position.set(-5, 5, 0);
    scene.add(pointLight);
    
    // Current avatar
    let currentAvatar = null;
    let currentModel = null;
    let mixer = null;
    let clock = new THREE.Clock();

    // Model paths
    const modelPaths = {
      abyss: '../../mrabyss/hooded-figure-with-scythe/source/hooded figure with sythe.glb',
      grimm: '../../PGD/grimm.glb',
      sweet: '../../biped/Animation_Walking_withSkin.fbx'
    };

    // Loaders
    const gltfLoader = new THREE.GLTFLoader();
    const fbxLoader = new THREE.FBXLoader();
    
    // Load 3D model from file
    function load3DModel(character, callback) {
      const path = modelPaths[character];
      if (!path) {
        console.error(`No model path for character: ${character}`);
        callback(null);
        return;
      }

      const isGLB = path.endsWith('.glb') || path.endsWith('.gltf');
      const loader = isGLB ? gltfLoader : fbxLoader;

      loader.load(
        path,
        (object) => {
          let model;
          if (isGLB) {
            model = object.scene;
            // Set up animations if available
            if (object.animations && object.animations.length > 0) {
              mixer = new THREE.AnimationMixer(model);
              const action = mixer.clipAction(object.animations[0]);
              action.play();
            }
          } else {
            model = object;
            // Set up FBX animations
            if (object.animations && object.animations.length > 0) {
              mixer = new THREE.AnimationMixer(model);
              const action = mixer.clipAction(object.animations[0]);
              action.play();
            }
          }

          // Scale and position the model
          model.scale.set(0.01, 0.01, 0.01); // Scale down from typical model size
          model.position.set(0, -2, -5);

          callback(model);
        },
        (progress) => {
          console.log(`Loading ${character}: ${(progress.loaded / progress.total * 100)}%`);
        },
        (error) => {
          console.error(`Error loading ${character} model:`, error);
          // Fallback to procedural model
          if (character === 'grimm') {
            callback(createGrimm());
          } else if (character === 'abyss') {
            callback(createAbyss());
          } else {
            callback(createSweet());
          }
        }
      );
    }

    // Create Grimm (Ragdoll) 3D Model - Fallback
    function createGrimm() {
      const group = new THREE.Group();
      
      // Body (brown fabric)
      const bodyGeometry = new THREE.CylinderGeometry(1.5, 2, 4, 8);
      const bodyMaterial = new THREE.MeshPhongMaterial({
        color: 0x8B4513,
        shininess: 30
      });
      const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
      body.position.y = 0;
      group.add(body);
      
      // Head
      const headGeometry = new THREE.SphereGeometry(1.2, 16, 16);
      const headMaterial = new THREE.MeshPhongMaterial({
        color: 0x8B4513,
        shininess: 20
      });
      const head = new THREE.Mesh(headGeometry, headMaterial);
      head.position.y = 3;
      group.add(head);
      
      // Button eyes
      const eyeGeometry = new THREE.CylinderGeometry(0.2, 0.2, 0.1, 16);
      const eyeMaterial = new THREE.MeshPhongMaterial({
        color: 0x000000,
        shininess: 100
      });
      
      const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
      leftEye.position.set(-0.4, 3.2, 1);
      leftEye.rotation.x = Math.PI / 2;
      group.add(leftEye);
      
      const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
      rightEye.position.set(0.4, 3.2, 1);
      rightEye.rotation.x = Math.PI / 2;
      group.add(rightEye);
      
      // Stitched mouth
      const mouthGeometry = new THREE.BoxGeometry(1, 0.1, 0.1);
      const mouthMaterial = new THREE.MeshBasicMaterial({ color: 0x000000 });
      const mouth = new THREE.Mesh(mouthGeometry, mouthMaterial);
      mouth.position.set(0, 2.5, 1);
      group.add(mouth);
      
      // Arms (dangling)
      const armGeometry = new THREE.CylinderGeometry(0.3, 0.3, 2);
      const leftArm = new THREE.Mesh(armGeometry, bodyMaterial);
      leftArm.position.set(-1.8, 0.5, 0);
      leftArm.rotation.z = 0.3;
      group.add(leftArm);
      
      const rightArm = new THREE.Mesh(armGeometry, bodyMaterial);
      rightArm.position.set(1.8, 0.5, 0);
      rightArm.rotation.z = -0.3;
      group.add(rightArm);
      
      group.position.set(0, -2, -5);
      group.scale.set(0.8, 0.8, 0.8);
      
      return group;
    }
    
    // Create Mr. Abyss (Hooded Figure with Scythe) 3D Model
    function createAbyss() {
      const group = new THREE.Group();
      
      // Body (dark robe)
      const bodyGeometry = new THREE.ConeGeometry(2, 5, 8);
      const bodyMaterial = new THREE.MeshPhongMaterial({
        color: 0x1a0033,
        shininess: 10,
        emissive: 0x2a0052,
        emissiveIntensity: 0.2
      });
      const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
      body.position.y = 0;
      group.add(body);
      
      // Hood
      const hoodGeometry = new THREE.ConeGeometry(1.5, 2, 8);
      const hoodMaterial = new THREE.MeshPhongMaterial({
        color: 0x000000,
        shininess: 5,
        emissive: 0x2a0052,
        emissiveIntensity: 0.3
      });
      const hood = new THREE.Mesh(hoodGeometry, hoodMaterial);
      hood.position.y = 3;
      group.add(hood);
      
      // Void face (black sphere inside hood)
      const voidGeometry = new THREE.SphereGeometry(0.8, 8, 8);
      const voidMaterial = new THREE.MeshBasicMaterial({
        color: 0x000000
      });
      const voidFace = new THREE.Mesh(voidGeometry, voidMaterial);
      voidFace.position.y = 2.8;
      voidFace.position.z = 0.3;
      group.add(voidFace);
      
      // Glowing eyes
      const eyeGeometry = new THREE.SphereGeometry(0.1, 8, 8);
      const eyeMaterial = new THREE.MeshBasicMaterial({
        color: 0xff0000,
        emissive: 0xff0000,
        emissiveIntensity: 2
      });
      
      const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
      leftEye.position.set(-0.3, 2.8, 1);
      group.add(leftEye);
      
      const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
      rightEye.position.set(0.3, 2.8, 1);
      group.add(rightEye);
      
      // Add point lights for glowing eyes
      const leftEyeLight = new THREE.PointLight(0xff0000, 0.5, 3);
      leftEyeLight.position.copy(leftEye.position);
      group.add(leftEyeLight);
      
      const rightEyeLight = new THREE.PointLight(0xff0000, 0.5, 3);
      rightEyeLight.position.copy(rightEye.position);
      group.add(rightEyeLight);
      
      // Scythe
      const handleGeometry = new THREE.CylinderGeometry(0.1, 0.1, 6);
      const handleMaterial = new THREE.MeshPhongMaterial({
        color: 0x4a4a4a,
        shininess: 80
      });
      const handle = new THREE.Mesh(handleGeometry, handleMaterial);
      handle.position.set(2.5, 1, 0);
      handle.rotation.z = 0.2;
      group.add(handle);
      
      // Scythe blade
      const bladeShape = new THREE.Shape();
      bladeShape.moveTo(0, 0);
      bladeShape.quadraticCurveTo(2, 0.5, 2.5, 2);
      bladeShape.lineTo(2.3, 2.2);
      bladeShape.quadraticCurveTo(1.8, 0.5, 0, 0.2);
      
      const bladeGeometry = new THREE.ExtrudeGeometry(bladeShape, {
        depth: 0.1,
        bevelEnabled: true,
        bevelThickness: 0.05,
        bevelSize: 0.05
      });
      
      const bladeMaterial = new THREE.MeshPhongMaterial({
        color: 0x888888,
        shininess: 100,
        emissive: 0x440088,
        emissiveIntensity: 0.1
      });
      
      const blade = new THREE.Mesh(bladeGeometry, bladeMaterial);
      blade.position.set(2.5, 3.5, 0);
      blade.rotation.z = -0.5;
      blade.scale.set(0.8, 0.8, 0.8);
      group.add(blade);
      
      group.position.set(0, -2, -5);
      group.scale.set(0.8, 0.8, 0.8);
      
      return group;
    }

    // Create Sweet (Stoned Philosopher) 3D Model - Fallback
    function createSweet() {
      const group = new THREE.Group();

      // Body (casual hoodie)
      const bodyGeometry = new THREE.CylinderGeometry(1.2, 1.5, 3.5, 8);
      const bodyMaterial = new THREE.MeshPhongMaterial({
        color: 0x228B22, // Forest green hoodie
        shininess: 20
      });
      const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
      body.position.y = 0;
      group.add(body);

      // Head
      const headGeometry = new THREE.SphereGeometry(1, 16, 16);
      const headMaterial = new THREE.MeshPhongMaterial({
        color: 0xFFDBB3, // Skin tone
        shininess: 30
      });
      const head = new THREE.Mesh(headGeometry, headMaterial);
      head.position.y = 2.5;
      group.add(head);

      // Eyes (sleepy/stoned look)
      const eyeGeometry = new THREE.SphereGeometry(0.15, 8, 8);
      const eyeMaterial = new THREE.MeshBasicMaterial({
        color: 0xFF0000, // Bloodshot
        emissive: 0x330000,
        emissiveIntensity: 0.3
      });

      const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
      leftEye.position.set(-0.3, 2.6, 0.8);
      group.add(leftEye);

      const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);
      rightEye.position.set(0.3, 2.6, 0.8);
      group.add(rightEye);

      // Mouth (slight smile)
      const mouthGeometry = new THREE.TorusGeometry(0.3, 0.05, 8, 16, Math.PI);
      const mouthMaterial = new THREE.MeshBasicMaterial({ color: 0x000000 });
      const mouth = new THREE.Mesh(mouthGeometry, mouthMaterial);
      mouth.position.set(0, 2.2, 0.8);
      mouth.rotation.z = Math.PI;
      group.add(mouth);

      // Arms
      const armGeometry = new THREE.CylinderGeometry(0.25, 0.25, 2);
      const leftArm = new THREE.Mesh(armGeometry, bodyMaterial);
      leftArm.position.set(-1.5, 0.5, 0);
      leftArm.rotation.z = 0.2;
      group.add(leftArm);

      const rightArm = new THREE.Mesh(armGeometry, bodyMaterial);
      rightArm.position.set(1.5, 0.5, 0);
      rightArm.rotation.z = -0.2;
      group.add(rightArm);

      // Chip bag in hand
      const chipGeometry = new THREE.BoxGeometry(0.3, 0.8, 0.1);
      const chipMaterial = new THREE.MeshPhongMaterial({
        color: 0xFFD700, // Golden chips bag
        shininess: 50
      });
      const chipBag = new THREE.Mesh(chipGeometry, chipMaterial);
      chipBag.position.set(1.8, 1.2, 0.3);
      chipBag.rotation.z = -0.3;
      group.add(chipBag);

      group.position.set(0, -2, -5);
      group.scale.set(0.8, 0.8, 0.8);

      return group;
    }

    // Animation functions
    function animateIdle(model) {
      if (!model) return;
      model.rotation.y = Math.sin(Date.now() * 0.001) * 0.1;
      model.position.y = -2 + Math.sin(Date.now() * 0.002) * 0.2;
    }
    
    function animateTalking(model) {
      if (!model) return;
      model.rotation.y = Math.sin(Date.now() * 0.005) * 0.2;
      model.position.y = -2 + Math.sin(Date.now() * 0.01) * 0.3;
      model.rotation.z = Math.sin(Date.now() * 0.008) * 0.05;
    }
    
    // Show avatar
    function showAvatar(character) {
      // Remove existing model
      if (currentModel) {
        scene.remove(currentModel);
        currentModel = null;
      }

      // Stop current animation mixer
      if (mixer) {
        mixer.stopAllAction();
        mixer = null;
      }

      // Load new model
      load3DModel(character, (model) => {
        if (model) {
          currentModel = model;
          scene.add(currentModel);
          currentAvatar = character;
          console.log(`${character} avatar loaded successfully`);
        } else {
          console.error(`Failed to load ${character} avatar`);
        }
      });
    }
    
    // Speaking state
    let isTalking = false;
    
    function speak(character, text) {
      showAvatar(character);
      
      const speechBubble = document.getElementById('speech');
      speechBubble.textContent = text;
      speechBubble.className = character;
      speechBubble.style.display = 'block';
      
      isTalking = true;
      
      setTimeout(() => {
        speechBubble.style.display = 'none';
        isTalking = false;
      }, 5000);
    }
    
    // Animation loop
    function animate() {
      requestAnimationFrame(animate);

      const delta = clock.getDelta();

      // Update animation mixer
      if (mixer) {
        mixer.update(delta);
      }

      if (currentModel) {
        if (isTalking) {
          animateTalking(currentModel);
        } else {
          animateIdle(currentModel);
        }
      }

      renderer.render(scene, camera);
    }
    
    // Camera setup
    camera.position.z = 5;
    camera.position.y = 2;
    
    // Start with Grimm
    showAvatar('grimm');
    
    // Listen for commands from main app
    window.addEventListener('message', (event) => {
      if (event.data.command === 'speak') {
        speak(event.data.character, event.data.text);
      } else if (event.data.command === 'show') {
        showAvatar(event.data.character);
      }
    });
    
    // Test with keyboard
    document.addEventListener('keydown', (e) => {
      if (e.key === 'g') {
        speak('grimm', 'Pretty good! That was absolutely dogshit gameplay!');
      } else if (e.key === 'a') {
        speak('abyss', 'The void witnesses your pathetic failure once again.');
      } else if (e.key === 's') {
        speak('sweet', 'Duuude, that was like... pretty intense, man. Pass the chips?');
      }
    });
    
    // Start animation
    animate();
    
    console.log('3D Avatars loaded! Press G for Grimm, A for Abyss, S for Sweet');
  </script>
</body>
</html>
